"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import Link from "next/link"
import { Navbar } from "@/components/layout/navbar"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { formatCurrency, formatDate } from "@/lib/utils"
import { useToast } from "@/hooks/use-toast"
import {
  Receipt,
  Search,
  Plus,
  Users,
  Calendar,
  DollarSign,
  CheckCircle,
  Clock,
  Eye,
  ArrowUpDown, User
} from "lucide-react"

interface Bill {
  _id: string
  title: string
  total: number
  creator: { name: string; _id: string }
  participants: Array<{
    user: { name: string; _id: string }
    amountOwed: number
    status: "paid" | "unpaid"
  }>
  createdAt: string
}

type SortField = "createdAt" | "total" | "title"
type SortOrder = "asc" | "desc"
type FilterStatus = "all" | "paid" | "unpaid" | "pending"

export default function BillsPage() {
  const { data: session } = useSession()
  const { toast } = useToast()
  const [bills, setBills] = useState<Bill[]>([])
  const [filteredBills, setFilteredBills] = useState<Bill[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [sortField, setSortField] = useState<SortField>("createdAt")
  const [sortOrder, setSortOrder] = useState<SortOrder>("desc")
  const [filterStatus, setFilterStatus] = useState<FilterStatus>("all")

  useEffect(() => {
    const fetchBills = async () => {
      try {
        const response = await fetch("/api/bills")
        if (response.ok) {
          const data = await response.json()
          setBills(data.bills || [])
        } else {
          toast({
            title: "Ошибка",
            description: "Не удалось загрузить счета",
            variant: "destructive"
          })
        }
      } catch (error) {
        console.error("Error fetching bills:", error)
        toast({
          title: "Ошибка",
          description: "Не удалось загрузить счета",
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }

    if (session?.user?.id) {
      fetchBills()
    }
  }, [session, toast])

  // Фильтрация и сортировка счетов
  useEffect(() => {
    const filtered = bills.filter(bill => {
      // Поиск по названию
      const matchesSearch = bill.title.toLowerCase().includes(searchQuery.toLowerCase())

      // Фильтрация по статусу
      let matchesStatus = true
      if (filterStatus !== "all") {
        const userParticipant = bill.participants.find(p => p.user._id === session?.user?.id)
        const isCreator = bill.creator._id === session?.user?.id

        if (filterStatus === "paid") {
          matchesStatus = userParticipant?.status === "paid" || isCreator
        } else if (filterStatus === "unpaid") {
          matchesStatus = userParticipant?.status === "unpaid" && !isCreator
        } else if (filterStatus === "pending") {
          matchesStatus = bill.participants.some(p => p.status === "unpaid")
        }
      }

      return matchesSearch && matchesStatus
    })

    // Сортировка
    filtered.sort((a, b) => {
      let aValue: number | string, bValue: number | string

      switch (sortField) {
        case "createdAt":
          aValue = new Date(a.createdAt).getTime()
          bValue = new Date(b.createdAt).getTime()
          break
        case "total":
          aValue = a.total
          bValue = b.total
          break
        case "title":
          aValue = a.title.toLowerCase()
          bValue = b.title.toLowerCase()
          break
        default:
          aValue = new Date(a.createdAt).getTime()
          bValue = new Date(b.createdAt).getTime()
      }

      if (sortOrder === "asc") {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    setFilteredBills(filtered)
  }, [bills, searchQuery, sortField, sortOrder, filterStatus, session])

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortOrder("desc")
    }
  }

  const getBillStatus = (bill: Bill) => {
    const userParticipant = bill.participants.find(p => p.user._id === session?.user?.id)
    const isCreator = bill.creator._id === session?.user?.id
    const allPaid = bill.participants.every(p => p.status === "paid")

    if (allPaid) return "completed"
    if (isCreator) return "creator"
    if (userParticipant?.status === "paid") return "paid"
    return "unpaid"
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800">Завершен</Badge>
      case "creator":
        return <Badge className="bg-blue-100 text-blue-800">Создатель</Badge>
      case "paid":
        return <Badge className="bg-green-100 text-green-800">Оплачено</Badge>
      case "unpaid":
        return <Badge className="bg-red-100 text-red-800">Не оплачено</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800">Неизвестно</Badge>
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Загрузка счетов...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Все счета</h1>
              <p className="text-gray-600 mt-1">
                Управляйте всеми своими счетами в одном месте
              </p>
            </div>
            <Link href="/bills/new">
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-2" />
                Новый счет
              </Button>
            </Link>
          </div>

          {/* Фильтры и поиск */}
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="flex flex-col md:flex-row gap-4">
                {/* Поиск */}
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Поиск по названию счета..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                {/* Фильтр по статусу */}
                <div className="flex gap-2">
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value as FilterStatus)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">Все счета</option>
                    <option value="unpaid">Не оплачено</option>
                    <option value="paid">Оплачено</option>
                    <option value="pending">Ожидают оплаты</option>
                  </select>
                </div>

                {/* Сортировка */}
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSort("createdAt")}
                    className="flex items-center gap-1"
                  >
                    <Calendar className="h-4 w-4" />
                    Дата
                    <ArrowUpDown className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSort("total")}
                    className="flex items-center gap-1"
                  >
                    <DollarSign className="h-4 w-4" />
                    Сумма
                    <ArrowUpDown className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSort("title")}
                    className="flex items-center gap-1"
                  >
                    Название
                    <ArrowUpDown className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Статистика */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <Receipt className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Всего счетов</p>
                    <p className="text-2xl font-bold text-gray-900">{bills.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Завершено</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {bills.filter(bill => bill.participants.every(p => p.status === "paid")).length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <Clock className="h-8 w-8 text-orange-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">В ожидании</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {bills.filter(bill => bill.participants.some(p => p.status === "unpaid")).length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <DollarSign className="h-8 w-8 text-purple-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Общая сумма</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(bills.reduce((sum, bill) => sum + bill.total, 0))}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Список счетов */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Receipt className="h-5 w-5 mr-2" />
                Счета ({filteredBills.length})
              </CardTitle>
              <CardDescription>
                {searchQuery && `Результаты поиска для "${searchQuery}"`}
                {filterStatus !== "all" && ` • Фильтр: ${filterStatus}`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {filteredBills.length === 0 ? (
                <div className="text-center py-12">
                  <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {bills.length === 0 ? "Пока нет счетов" : "Счета не найдены"}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {bills.length === 0
                      ? "Создайте свой первый счет, чтобы начать разделять расходы с друзьями"
                      : "Попробуйте изменить параметры поиска или фильтры"
                    }
                  </p>
                  {bills.length === 0 && (
                    <Link href="/bills/new">
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Создать счет
                      </Button>
                    </Link>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredBills.map((bill) => {
                    const status = getBillStatus(bill)
                    const userParticipant = bill.participants.find(p => p.user._id === session?.user?.id)
                    const totalPaid = bill.participants.filter(p => p.status === "paid").length
                    const totalParticipants = bill.participants.length

                    return (
                      <div
                        key={bill._id}
                        className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h3 className="text-lg font-semibold text-gray-900">
                                {bill.title}
                              </h3>
                              {getStatusBadge(status)}
                            </div>

                            <div className="flex items-center gap-6 text-sm text-gray-600 mb-3">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-4 w-4" />
                                {formatDate(new Date(bill.createdAt))}
                              </div>
                              <div className="flex items-center gap-1">
                                <DollarSign className="h-4 w-4" />
                                {formatCurrency(bill.total)}
                              </div>
                              <div className="flex items-center gap-1">
                                <Users className="h-4 w-4" />
                                {bill.participants.length} участник{bill.participants.length > 1 ? 'ов' : ''}
                              </div>
                              <div className="flex items-center gap-1">
                                <User className="h-4 w-4" />
                                Создатель: {bill.creator.name}
                              </div>
                            </div>

                            {/* Прогресс оплаты */}
                            <div className="flex items-center gap-2 mb-2">
                              <div className="flex-1 bg-gray-200 rounded-full h-2">
                                <div
                                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                                  style={{ width: `${(totalPaid / totalParticipants) * 100}%` }}
                                ></div>
                              </div>
                              <span className="text-sm text-gray-600">
                                {totalPaid}/{totalParticipants} оплачено
                              </span>
                            </div>

                            {/* Информация о долге пользователя */}
                            {userParticipant && (
                              <div className="text-sm">
                                <span className="text-gray-600">Ваш долг: </span>
                                <span className={`font-medium ${
                                  userParticipant.status === "paid"
                                    ? "text-green-600"
                                    : "text-red-600"
                                }`}>
                                  {formatCurrency(userParticipant.amountOwed)}
                                  {userParticipant.status === "paid" && " (оплачено)"}
                                </span>
                              </div>
                            )}
                          </div>

                          <div className="flex items-center gap-2">
                            <Link href={`/bills/${bill._id}`}>
                              <Button variant="outline" size="sm">
                                <Eye className="h-4 w-4 mr-2" />
                                Просмотр
                              </Button>
                            </Link>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
