"use client"

import { useSession } from "next-auth/react"
import { useEffect, useState } from "react"
import Link from "next/link"
import { Navbar } from "@/components/layout/navbar"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { formatCurrency, formatDate } from "@/lib/utils"
import {
  Plus,
  Receipt,
  Users,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle
} from "lucide-react"

interface Bill {
  _id: string
  title: string
  total: number
  creator: { name: string; _id: string }
  participants: Array<{
    user: { name: string; _id: string }
    amountOwed: number
    status: "paid" | "unpaid"
  }>
  createdAt: string
}

export default function DashboardPage() {
  const { data: session } = useSession()
  const [bills, setBills] = useState<Bill[]>([])
  const [stats, setStats] = useState({
    totalOwed: 0,
    totalOwing: 0,
    pendingBills: 0,
    completedBills: 0
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Загружаем статистику
        const statsResponse = await fetch("/api/dashboard/stats")
        if (statsResponse.ok) {
          const statsData = await statsResponse.json()
          setStats(statsData.stats)
        }

        // Загружаем счета
        const billsResponse = await fetch("/api/bills")
        if (billsResponse.ok) {
          const billsData = await billsResponse.json()
          setBills(billsData.bills)
        }
      } catch (error) {
        console.error("Error fetching dashboard data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Загрузка...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Добро пожаловать, {session?.user?.name}!
            </h1>
            <p className="text-gray-600 mt-1">
              Управляйте своими счетами и следите за расходами
            </p>
          </div>
          <Link href="/bills/new">
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              Новый счет
            </Button>
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Вам должны</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(stats.totalOwed)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Вы должны</CardTitle>
              <AlertCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {formatCurrency(stats.totalOwing)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Ожидают оплаты</CardTitle>
              <Clock className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {stats.pendingBills}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Завершено</CardTitle>
              <CheckCircle className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {stats.completedBills}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Bills */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <Receipt className="h-5 w-5 mr-2" />
                Последние счета
              </div>
              <Link href="/bills">
                <Button variant="outline" size="sm">
                  Все счета
                </Button>
              </Link>
            </CardTitle>
            <CardDescription>
              Ваши недавние счета и их статус
            </CardDescription>
          </CardHeader>
          <CardContent>
            {bills.length === 0 ? (
              <div className="text-center py-8">
                <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Пока нет счетов
                </h3>
                <p className="text-gray-600 mb-4">
                  Создайте свой первый счет, чтобы начать разделять расходы с друзьями
                </p>
                <Link href="/bills/new">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Создать счет
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="space-y-4">
                {bills.map((bill) => (
                  <div
                    key={bill._id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{bill.title}</h3>
                      <p className="text-sm text-gray-600">
                        {formatDate(new Date(bill.createdAt))} • {formatCurrency(bill.total)}
                      </p>
                      <div className="flex items-center mt-2 space-x-4">
                        {bill.participants.map((participant, index) => (
                          <span
                            key={index}
                            className={`text-xs px-2 py-1 rounded-full ${
                              participant.status === 'paid'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-orange-100 text-orange-800'
                            }`}
                          >
                            {participant.user.name}: {formatCurrency(participant.amountOwed)}
                          </span>
                        ))}
                      </div>
                    </div>
                    <Link href={`/bills/${bill._id}`}>
                      <Button variant="outline" size="sm">
                        Подробнее
                      </Button>
                    </Link>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Быстрые действия
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link href="/bills/new" className="block">
                <Button variant="outline" className="w-full justify-start">
                  <Plus className="h-4 w-4 mr-2" />
                  Создать новый счет
                </Button>
              </Link>
              <Link href="/friends" className="block">
                <Button variant="outline" className="w-full justify-start">
                  <Users className="h-4 w-4 mr-2" />
                  Управление друзьями
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Советы</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm text-gray-600">
                <p>💡 Добавляйте друзей для быстрого создания счетов</p>
                <p>📱 Отмечайте платежи сразу после получения</p>
                <p>🔔 Следите за уведомлениями о новых счетах</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
