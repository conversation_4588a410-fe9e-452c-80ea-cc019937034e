"use client"

import { useSession } from "next-auth/react"
import { useEffect, useState } from "react"
import Link from "next/link"
import { Navbar } from "@/components/layout/navbar"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { formatCurrency, formatDate } from "@/lib/utils"
import {
  Plus,
  Receipt,
  Users,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle
} from "lucide-react"

interface Bill {
  _id: string
  title: string
  total: number
  creator: { name: string; _id: string }
  participants: Array<{
    user: { name: string; _id: string }
    amountOwed: number
    status: "paid" | "unpaid"
  }>
  createdAt: string
}

export default function DashboardPage() {
  const { data: session } = useSession()
  const [bills, setBills] = useState<Bill[]>([])
  const [stats, setStats] = useState({
    totalOwed: 0,
    totalOwing: 0,
    pendingBills: 0,
    completedBills: 0
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Загружаем статистику
        const statsResponse = await fetch("/api/dashboard/stats")
        if (statsResponse.ok) {
          const statsData = await statsResponse.json()
          setStats(statsData.stats)
        }

        // Загружаем счета
        const billsResponse = await fetch("/api/bills")
        if (billsResponse.ok) {
          const billsData = await billsResponse.json()
          setBills(billsData.bills)
        }
      } catch (error) {
        console.error("Error fetching dashboard data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="relative">
                <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
                <div className="absolute inset-0 rounded-full h-16 w-16 border-4 border-transparent border-r-purple-400 animate-spin mx-auto" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
              </div>
              <p className="mt-6 text-lg text-gray-600 font-medium">Загрузка дашборда...</p>
              <p className="mt-2 text-sm text-gray-500">Подготавливаем ваши данные</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30">
      <Navbar />

      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-10 gap-4">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              Добро пожаловать, {session?.user?.name}!
            </h1>
            <p className="text-lg text-gray-600">
              Управляйте своими счетами и следите за расходами
            </p>
          </div>
          <Link href="/bills/new">
            <Button className="bg-blue-600 hover:bg-blue-700 border-0 shadow-lg px-8 py-3 text-base">
              <Plus className="h-5 w-5 mr-2" />
              Новый счет
            </Button>
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
          <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white border-0 shadow-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-medium opacity-90">Вам должны</CardTitle>
              <TrendingUp className="h-5 w-5 opacity-90" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold mb-2">
                {formatCurrency(stats.totalOwed)}
              </div>
              <p className="text-xs opacity-90">К получению</p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-medium text-gray-700">Вы должны</CardTitle>
              <AlertCircle className="h-5 w-5 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-red-600 mb-2">
                {formatCurrency(stats.totalOwing)}
              </div>
              <p className="text-xs text-gray-600">К оплате</p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-medium text-gray-700">Ожидают оплаты</CardTitle>
              <Clock className="h-5 w-5 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-orange-600 mb-2">
                {stats.pendingBills}
              </div>
              <p className="text-xs text-gray-600">Активных счетов</p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-medium text-gray-700">Завершено</CardTitle>
              <CheckCircle className="h-5 w-5 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {stats.completedBills}
              </div>
              <p className="text-xs text-gray-600">Оплаченных счетов</p>
            </CardContent>
          </Card>
        </div>

        {/* Recent Bills */}
        <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm mb-8">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center justify-between text-xl">
              <div className="flex items-center">
                <Receipt className="h-6 w-6 mr-3 text-blue-600" />
                Последние счета
              </div>
              <Link href="/bills">
                <Button variant="outline" size="sm" className="border-0 bg-blue-50 hover:bg-blue-100 text-blue-600">
                  Все счета
                </Button>
              </Link>
            </CardTitle>
            <CardDescription className="text-gray-600">
              Ваши недавние счета и их статус
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-2">
            {bills.length === 0 ? (
              <div className="text-center py-12">
                <Receipt className="h-16 w-16 text-gray-300 mx-auto mb-6" />
                <h3 className="text-xl font-medium text-gray-900 mb-3">
                  Пока нет счетов
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  Создайте свой первый счет, чтобы начать разделять расходы с друзьями
                </p>
                <Link href="/bills/new">
                  <Button className="bg-blue-600 hover:bg-blue-700 border-0 shadow-lg px-8 py-3">
                    <Plus className="h-5 w-5 mr-2" />
                    Создать счет
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="space-y-4">
                {bills.map((bill) => (
                  <div
                    key={bill._id}
                    className="flex items-center justify-between p-4 rounded-xl bg-gray-50/50 hover:bg-gray-100/50 transition-colors border-0"
                  >
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-1">{bill.title}</h3>
                      <p className="text-sm text-gray-600 mb-3">
                        {formatDate(new Date(bill.createdAt))} • {formatCurrency(bill.total)}
                      </p>
                      <div className="flex items-center flex-wrap gap-2">
                        {bill.participants.map((participant, index) => (
                          <span
                            key={index}
                            className={`text-xs px-3 py-1 rounded-full font-medium ${
                              participant.status === 'paid'
                                ? 'bg-green-100 text-green-700 border border-green-200'
                                : 'bg-orange-100 text-orange-700 border border-orange-200'
                            }`}
                          >
                            {participant.user.name}: {formatCurrency(participant.amountOwed)}
                          </span>
                        ))}
                      </div>
                    </div>
                    <Link href={`/bills/${bill._id}`}>
                      <Button variant="outline" size="sm" className="border-0 bg-blue-50 hover:bg-blue-100 text-blue-600">
                        Подробнее
                      </Button>
                    </Link>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center text-lg">
                <Users className="h-6 w-6 mr-3 text-purple-600" />
                Быстрые действия
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 pt-2">
              <Link href="/bills/new" className="block">
                <Button variant="outline" className="w-full justify-start border-0 bg-blue-50 hover:bg-blue-100 text-blue-700 py-3">
                  <Plus className="h-5 w-5 mr-3" />
                  Создать новый счет
                </Button>
              </Link>
              <Link href="/friends" className="block">
                <Button variant="outline" className="w-full justify-start border-0 bg-purple-50 hover:bg-purple-100 text-purple-700 py-3">
                  <Users className="h-5 w-5 mr-3" />
                  Управление друзьями
                </Button>
              </Link>
              <Link href="/analytics" className="block">
                <Button variant="outline" className="w-full justify-start border-0 bg-green-50 hover:bg-green-100 text-green-700 py-3">
                  <TrendingUp className="h-5 w-5 mr-3" />
                  Аналитика расходов
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center text-lg">
                <div className="w-6 h-6 bg-yellow-500 rounded-lg flex items-center justify-center text-white text-sm mr-3">
                  💡
                </div>
                Полезные советы
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-2">
              <div className="space-y-4">
                <div className="p-4 bg-gradient-to-r from-blue-50 to-blue-100/50 rounded-xl border border-blue-100/50">
                  <p className="text-sm text-blue-800 font-medium">💡 Добавляйте друзей для быстрого создания счетов</p>
                </div>
                <div className="p-4 bg-gradient-to-r from-green-50 to-green-100/50 rounded-xl border border-green-100/50">
                  <p className="text-sm text-green-800 font-medium">📱 Отмечайте платежи сразу после получения</p>
                </div>
                <div className="p-4 bg-gradient-to-r from-purple-50 to-purple-100/50 rounded-xl border border-purple-100/50">
                  <p className="text-sm text-purple-800 font-medium">🔔 Следите за уведомлениями о новых счетах</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
